DROP DATABASE IF EXISTS acr;

CREATE DATABASE acr;

\c acr

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE t_farmer_group (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id)
);

CREATE TABLE t_farmer (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  farmer_reference TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  phone_number TEXT NOT NULL,
  farmer_group_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id),
  FOREIGN KEY (farmer_group_id) REFERENCES t_farmer_group(id) ON DELETE RESTRICT
);
CREATE INDEX idx_farmer_group_id ON t_farmer(farmer_group_id);
CREATE INDEX idx_farmer_reference ON t_farmer(farmer_reference);

CREATE TABLE t_activity_type (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id)
);
CREATE INDEX idx_activity_type_name ON t_activity_type(name);

CREATE TABLE t_payment_contract (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  activity_type_id UUID NOT NULL,
  rate NUMERIC(14,2) NOT NULL,
  currency TEXT NOT NULL CHECK (currency ~ '^[A-Z]{3}$'),
  unit TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id),
  FOREIGN KEY (activity_type_id) REFERENCES t_activity_type(id) ON DELETE RESTRICT
);
CREATE INDEX idx_payment_contract_activity_type_id ON t_payment_contract(activity_type_id);

CREATE TABLE t_activity (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  activity_reference TEXT NOT NULL UNIQUE,
  farmer_id UUID NOT NULL,
  activity_type_id UUID NOT NULL,
  payment_contract_id UUID NOT NULL,
  quantity NUMERIC(12,2) NOT NULL,
  unit TEXT NOT NULL,
  date_completed DATE NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id),
  FOREIGN KEY (farmer_id) REFERENCES t_farmer(id) ON DELETE RESTRICT,
  FOREIGN KEY (activity_type_id) REFERENCES t_activity_type(id) ON DELETE RESTRICT,
  FOREIGN KEY (payment_contract_id) REFERENCES t_payment_contract(id) ON DELETE RESTRICT
);
CREATE INDEX idx_activity_farmer_id ON t_activity(farmer_id);
CREATE INDEX idx_activity_type_id ON t_activity(activity_type_id);
CREATE INDEX idx_activity_contract_id ON t_activity(payment_contract_id);
CREATE INDEX idx_activity_date_completed ON t_activity(date_completed);
CREATE INDEX idx_activity_farmer_date ON t_activity(farmer_id, date_completed);

CREATE TABLE t_payment (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  farmer_id UUID NOT NULL,
  payment_reference TEXT NOT NULL UNIQUE,
  total_amount NUMERIC(14,2) NOT NULL,
  currency TEXT NOT NULL CHECK (currency ~ '^[A-Z]{3}$'),
  description TEXT,
  payment_date DATE NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (id),
  FOREIGN KEY (farmer_id) REFERENCES t_farmer(id) ON DELETE RESTRICT
);
CREATE INDEX idx_payment_farmer_id ON t_payment(farmer_id);
CREATE INDEX idx_payment_date ON t_payment(payment_date);

CREATE TABLE t_payment_item (
  id UUID NOT NULL DEFAULT uuid_generate_v4(),
  payment_id UUID NOT NULL,
  activity_id UUID NOT NULL,
  payment_contract_id UUID NOT NULL,
  amount NUMERIC(14,2) NOT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (payment_id) REFERENCES t_payment(id) ON DELETE CASCADE,
  FOREIGN KEY (activity_id) REFERENCES t_activity(id) ON DELETE RESTRICT,
  FOREIGN KEY (payment_contract_id) REFERENCES t_payment_contract(id) ON DELETE RESTRICT
);
CREATE INDEX idx_payment_item_payment_id ON t_payment_item(payment_id);
CREATE INDEX idx_payment_item_contract_id ON t_payment_item(payment_contract_id);


-- Seed some initial data
INSERT INTO t_farmer_group (
  name
) VALUES ('Central'), ('Eastern'), ('Northern'), ('Western');
