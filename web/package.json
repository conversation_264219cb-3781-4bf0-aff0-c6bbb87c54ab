{"name": "@acr/web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.63.0", "react-router": "^7.9.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.3", "eslint": "^9.36.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "prettier": "^3.6.2", "tw-animate-css": "^1.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.7"}}