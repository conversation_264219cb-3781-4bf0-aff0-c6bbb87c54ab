import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { TypographyH2 } from "@/components/ui/typography";
import { RecycleIcon, TractorIcon, UploadIcon } from "lucide-react";
import { Link } from "react-router";

export const GlobalSidebar = () => {
  return (
    <Sidebar>
        <SidebarHeader>
          <div className="flex items-center gap-2 pl-2">
          <RecycleIcon />
          <TypographyH2>
          AgriCarbonRemoval
          </TypographyH2>
          </div>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>
              Navigation
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                  <Link to="/">
                  <UploadIcon />
                  Upload
                  </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                   <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                  <Link to="/farmers">
                  <TractorIcon />
                  Farmers
                  </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

    </Sidebar>
  );
};
