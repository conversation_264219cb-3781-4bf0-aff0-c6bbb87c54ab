import { UploadIcon } from "lucide-react";
import { Dropzone, DropzoneEmptyState, DropzoneContent } from "../../ui/shadcn-io/dropzone";
import { acceptedFileTypes } from "@/utils/constants";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { TypographyP } from "@/components/ui/typography";
import { useFileStore } from "@/stores/useFileStore";

export const UploadDropzone = () => {
  const fileStore = useFileStore();

  const handleDrop = async (files: File[]) => {
    await fileStore.classify(files);
  };

  const handleError = (error: Error) => {
    toast('File type invalid', {
      description: error.message
    });
  };

  return (
    <Dropzone onDrop={handleDrop} onError={handleError} src={fileStore.rawFiles} className="cursor-pointer" accept={acceptedFileTypes} maxFiles={10}>
      <DropzoneEmptyState>
          <div className="flex w-full items-center gap-4 p-8">
            <div className="flex size-16 items-center justify-center rounded-lg bg-muted text-muted-foreground">
              <UploadIcon size={24} />
            </div>
            <div className="text-left">
              <p className="font-medium text-sm">Upload a file</p>
              <p className="text-muted-foreground text-xs">
                Drag and drop or click to upload
              </p>
            </div>
          </div>

      </DropzoneEmptyState>
      <DropzoneContent>
        {fileStore.progress !== undefined ? (
          <>
            <TypographyP>
              Uploading
            </TypographyP>
            <Skeleton className="h-[20px] w-full rounded-full" />
          </>
         ) : null}
      </DropzoneContent>
    </Dropzone>
  );
};
