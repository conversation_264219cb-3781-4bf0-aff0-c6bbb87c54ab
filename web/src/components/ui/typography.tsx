export const TypographyH1 = ({ children }: { children: React.ReactNode }) => {
  return (
    <h1 className="scroll-m-20 text-2xl font-extrabold tracking-tight text-balance">{children}</h1>
  );
};

export const TypographyH2 = ({ children }: { children: React.ReactNode }) => {
  return <h2 className="scroll-m-20 text-xl font-semibold tracking-tight">{children}</h2>;
};

export const TypographyP = ({ children }: { children: React.ReactNode }) => {
  return <p className="leading-7">{children}</p>;
};
