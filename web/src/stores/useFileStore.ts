import { create } from "zustand";
import { persist } from "zustand/middleware";
import { toast } from "sonner";
import { parseFile } from "@/utils/parse";
import type { ParsedResult } from "@/types/parsed.types";

interface FileState {
  progress: number | undefined;
  rawFiles: File[],
  parsedFiles: ParsedResult[];
  classify: (files: File[]) => Promise<
    | { successfullyParsed: ParsedResult[], unsuccessfullyParsed: File[] }
    | undefined
  >;
}

export const useFileStore = create<FileState>()(
  persist(
    (set) => ({
      progress: undefined,
      parsedFiles: [],
      rawFiles: [],
      classify: async (files) => {
        set({ progress: 0});
        set({ rawFiles: files });

        const successfullyParsed: ParsedResult[] = [];
        const unsuccessfullyParsed: File[] = [];

        try {
          const parsed = await Promise.all(files.map((file) => {
            try {
              return parseFile(file);
            } catch {
              unsuccessfullyParsed.push(file);
              return;
            }
          }));

          successfullyParsed.push(...parsed.filter((result): result is ParsedResult => result !== undefined));

          set({ parsedFiles: successfullyParsed });

          set({ progress: undefined });
          return {
            successfullyParsed,
            unsuccessfullyParsed
          };
        } catch (error: unknown) {
           toast('Failed to classify files', {
      description: (error as Error).message
    });
        } finally {
          set({ progress: undefined });
        }
      },
    }),
    {
      name: "file-store",
    },
  )
)
