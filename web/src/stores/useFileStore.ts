import { create } from "zustand";
import { persist } from "zustand/middleware";
import { toast } from "sonner";
import { parseFile } from "@/utils/parse";
import type { ParsedResult } from "@/types/parsed.types";

interface FileState {
  progress: number | undefined;
  rawFiles: File[],
  parsedFiles: ParsedResult[];
  classify: (files: File[]) => Promise<ParsedResult[] | undefined>;
}

export const useFileStore = create<FileState>()(
  persist(
    (set) => ({
      progress: undefined,
      parsedFiles: [],
      rawFiles: [],
      classify: async (files) => {
        set({ progress: 0});
        set({ rawFiles: files });
        try {
          const parsed = await Promise.all(files.map(parseFile));
          set({ progress: 30 });
          set({ parsedFiles: parsed });

          return parsed;
        } catch (error: unknown) {
           toast('Failed to classify files', {
      description: (error as Error).message
    });
        } finally {
          set({ progress: undefined });
        }
      },
    }),
    {
      name: "file-store",
    },
  )
)
