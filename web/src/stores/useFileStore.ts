import { create } from "zustand";
import { persist } from "zustand/middleware";
import { toast } from "sonner";
import { parseFile } from "@/utils/parse";
import type { ParsedResult, ParseError, ClassifyResult } from "@/types/parsed.types";

interface FileState {
  progress: number | undefined;
  rawFiles: File[];
  parse: (files: File[]) => Promise<ClassifyResult | undefined>;
}

export const useFileStore = create<FileState>()(
  persist(
    (set) => ({
      progress: undefined,
      rawFiles: [],
      parse: async (files) => {
        set({ progress: 0 });
        set({ rawFiles: files });

        const successfullyParsed: ParsedResult[] = [];
        const unsuccessfullyParsed: ParseError[] = [];

        try {
          const results = await Promise.allSettled(
            files.map(async (file) => {
              try {
                const parsed = await parseFile(file);
                return { success: true as const, data: parsed, file };
              } catch (error) {
                const errorMessage =
                  error instanceof Error ? error.message : "Unknown parsing error";
                return { success: false as const, error: errorMessage, file };
              }
            }),
          );

          results.forEach((result) => {
            if (result.status === "fulfilled") {
              if (result.value.success) {
                successfullyParsed.push(result.value.data);
              } else {
                unsuccessfullyParsed.push({
                  file: result.value.file,
                  error: result.value.error,
                });
              }
            }
          });

          console.log({
            successfullyParsed,
            unsuccessfullyParsed,
          });

          return {
            successfullyParsed,
            unsuccessfullyParsed,
          };
        } catch (error: unknown) {
          toast("Failed to parse files", {
            description: (error as Error).message,
          });
        } finally {
          set({ progress: undefined });
        }
      },
    }),
    {
      name: "file-store",
    },
  ),
);
