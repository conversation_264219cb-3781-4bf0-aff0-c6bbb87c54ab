import { Outlet } from "react-router";
import { Toaster } from "@/components/ui/sonner"
import { GlobalSidebar } from "@/components/features/layout/GlobalSidebar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";

export const DefaultLayout = () => {
  return <>
      <SidebarProvider>
      <GlobalSidebar />

      <SidebarTrigger />

      <div className="min-h-screen px-4 w-full">
      <Outlet />
      </div>
       </SidebarProvider>

       <Toaster />
    </>
};
