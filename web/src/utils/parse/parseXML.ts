import type { ParsedResult } from "@/types/parsed.types";

export const parseXML = async (file: File): Promise<ParsedResult> => {
  const text = await file.text();
  const parser = new DOMParser();
  const doc = parser.parseFromString(text, "application/xml");

  const contracts = Array.from(doc.getElementsByTagName("contract"));
  const result = contracts.map((c) => ({
    activity_type: c.getElementsByTagName("activity_type")[0]?.textContent ?? "",
    rate: c.getElementsByTagName("rate")[0]?.textContent ?? "",
    currency: c.getElementsByTagName("currency")[0]?.textContent ?? "",
    unit: c.getElementsByTagName("unit")[0]?.textContent ?? "",
    description: c.getElementsByTagName("description")[0]?.textContent ?? "",
  }));
  return result;
};
