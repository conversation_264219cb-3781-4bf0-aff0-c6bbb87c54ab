import { parseCSV } from "./parseCSV";
import { parse<PERSON>SO<PERSON> } from "./parseJSON";
import { parseXML } from "./parseXML";

export const parseFile = async (file: File) => {
  const ext = file.name.split(".").pop()?.toLowerCase();

  if (!ext) throw new Error("Cannot determine file extension");

  if (ext === "csv") {
    return parseCSV(file);
  } else if (ext === "json") {
    return parseJSON(file);
  } else if (ext === "xml") {
    return parseXML(file);
  } else {
    throw new Error(`Unsupported file type: ${ext}`);
  }
};
