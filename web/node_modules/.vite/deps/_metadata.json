{"hash": "df2e3883", "configHash": "bc6b1d37", "lockfileHash": "26d7e168", "browserHash": "256e79ed", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "d1d6d85e", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "95343f0b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4e828c4a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "677593b0", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "4992aa40", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "11c9617e", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "175377b5", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "3d39ca6e", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "b59b7869", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "36d903be", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "696da981", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "2c56e0a1", "needsInterop": false}, "papaparse": {"src": "../../../../node_modules/papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "33b361c9", "needsInterop": true}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "72412fbe", "needsInterop": true}, "react-dropzone": {"src": "../../../../node_modules/react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "e1447b1e", "needsInterop": false}, "react-router": {"src": "../../../../node_modules/react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "6cadcf3b", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "95184148", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "931e641f", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "fa1d2b04", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "c30a6c90", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-UIV6C5BN": {"file": "chunk-UIV6C5BN.js"}, "chunk-KN55GZJK": {"file": "chunk-KN55GZJK.js"}, "chunk-IDUMAKIR": {"file": "chunk-IDUMAKIR.js"}, "chunk-B5GDCTOC": {"file": "chunk-B5GDCTOC.js"}, "chunk-5GBUHKNR": {"file": "chunk-5GBUHKNR.js"}, "chunk-SBOXZC2N": {"file": "chunk-SBOXZC2N.js"}, "chunk-7HQUOLNA": {"file": "chunk-7HQUOLNA.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}