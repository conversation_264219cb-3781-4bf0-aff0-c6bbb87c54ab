{"hash": "df2e3883", "configHash": "bc6b1d37", "lockfileHash": "26d7e168", "browserHash": "f5d7744e", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "e37e4630", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "e88753a1", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "78edc8a3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5e91b6eb", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "ece5cd4d", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "8a749abc", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "8ad6b0f1", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "90fb72cb", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "63fc5f74", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "5b63f070", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4fc3ad7b", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "02a28eff", "needsInterop": false}, "papaparse": {"src": "../../../../node_modules/papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "56bd03f8", "needsInterop": true}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d7ef5156", "needsInterop": true}, "react-dropzone": {"src": "../../../../node_modules/react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "94d2d375", "needsInterop": false}, "react-router": {"src": "../../../../node_modules/react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "1307a7da", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "991872d9", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "e251d3f5", "needsInterop": false}}, "chunks": {"chunk-UIV6C5BN": {"file": "chunk-UIV6C5BN.js"}, "chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-VAW666AU": {"file": "chunk-VAW666AU.js"}, "chunk-25CAM5OV": {"file": "chunk-25CAM5OV.js"}, "chunk-B5GDCTOC": {"file": "chunk-B5GDCTOC.js"}, "chunk-SBOXZC2N": {"file": "chunk-SBOXZC2N.js"}, "chunk-5GBUHKNR": {"file": "chunk-5GBUHKNR.js"}, "chunk-7HQUOLNA": {"file": "chunk-7HQUOLNA.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}