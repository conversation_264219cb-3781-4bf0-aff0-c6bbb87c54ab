{"hash": "141a5bf5", "configHash": "bc6b1d37", "lockfileHash": "8fe70c83", "browserHash": "03827c79", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "f6109b3e", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "22db6f00", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9bb1e166", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "926c3b50", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "8ce5669e", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "612004ae", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "fe60b05c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "c3c8635c", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "70ae618c", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "c2771f35", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a96acd37", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "7f8a4d40", "needsInterop": false}, "papaparse": {"src": "../../../../node_modules/papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "d05bcb29", "needsInterop": true}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b10b1b20", "needsInterop": true}, "react-dropzone": {"src": "../../../../node_modules/react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "07da2f6a", "needsInterop": false}, "react-router": {"src": "../../../../node_modules/react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "ed6caab3", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "aa14894f", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "e49d044e", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "1f396f02", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "0746c598", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-UIV6C5BN": {"file": "chunk-UIV6C5BN.js"}, "chunk-DF6OMOTV": {"file": "chunk-DF6OMOTV.js"}, "chunk-SOUKKYSY": {"file": "chunk-SOUKKYSY.js"}, "chunk-SBOXZC2N": {"file": "chunk-SBOXZC2N.js"}, "chunk-B5GDCTOC": {"file": "chunk-B5GDCTOC.js"}, "chunk-5GBUHKNR": {"file": "chunk-5GBUHKNR.js"}, "chunk-7HQUOLNA": {"file": "chunk-7HQUOLNA.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}