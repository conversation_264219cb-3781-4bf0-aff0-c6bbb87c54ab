import { z } from "zod";
import { farmerGroupSchema } from "./farmerGroup";

export const farmerSchema = z.object({
  id: z.uuid({ version: "v4" }),
  farmerReference: z.string(),
  name: z.string(),
  farmerGroup: farmerGroupSchema,
  phoneNumber: z.string(),
  createdAt: z.coerce.date().optional(),
});

export const newFarmerSchema = farmerSchema.pick({
  farmerReference: true,
  name: true,
  phoneNumber: true,
}).extend({
  farmerGroupId: z.uuid({ version: "v4" }),
});

export const getFarmersSchema = z.object({
  names: z.array(z.string()).optional(),
});
