import { z } from "zod";

export const rawRowsSchema = z.array(z.record(z.string(), z.unknown()));

export const classifyFarmerSchema = z.object({
  farmer_id: z.string(),
  name: z.string(),
  phone_number: z.string(),
  group_name: z.string(),
});


export const classifyActivitySchema = z.object({
  activity_id: z.string(),
  farmer_id: z.string(),
  activity_type: z.string(),
  quantity: z.string(),
  date_completed: z.string(),
});

export const classifyContractSchema = z.object({
  activity_type: z.string(),
  rate: z.union([z.string(), z.number()]),
  currency: z.string(),
  unit: z.string(),
});
