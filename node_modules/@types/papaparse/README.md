# Installation
> `npm install --save @types/papaparse`

# Summary
This package contains type definitions for papaparse (https://github.com/mholt/PapaParse).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/papaparse.

### Additional Details
 * Last updated: Wed, 07 May 2025 01:30:00 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>](https://github.com/torpedro), [<PERSON>](https://github.com/rainshen49), [<PERSON>](https://github.com/jfloff), [<PERSON>](https://github.com/johnnyreilly), [<PERSON>](https://github.com/albertorestifo), [<PERSON><PERSON>](https://github.com/jliuhtonen), [<PERSON><PERSON><PERSON>](https://github.com/rbarbazz), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/emmanuel<PERSON>tier), [Opportunity Liu](https://github.com/OpportunityLiu), and [Kohei Matsubara](https://github.com/matsuby).
