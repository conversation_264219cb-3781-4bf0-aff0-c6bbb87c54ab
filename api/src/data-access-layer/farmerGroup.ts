import { query } from "@/lib/database";
import { deserialize } from "@/models/farmerGroup";
import { FarmerGroup, GetFarmerGroups } from "@acr/common/types/farmerGroup.types";

export const getFarmerGroups = async (filters: GetFarmerGroups): Promise<FarmerGroup[]> => {
  const { names } = filters;

  const namesLike = names?.map((name: string) => `%${name}%`);

  const result = await query({
    query: `
      SELECT * FROM t_farmer_group fg WHERE (($1::TEXT[] IS NULL) OR fg.name LIKE ANY($1::TEXT[]))
    `,
    values: [
      namesLike
    ]
  });

  return deserialize(result);
};
