import express from "express";
import { statusRoutes } from "@/routes/v1/public/status";
import { farmerGroupsRoutes } from "@/routes/v1/public/farmerGroups";
import { classifierRoutes } from "./classify";

const router = express.Router();

router.use("/classify", classifierRoutes)
router.use("/status", statusRoutes);
router.use("/farmer-groups", farmerGroupsRoutes);

export const publicV1Routes = router;
