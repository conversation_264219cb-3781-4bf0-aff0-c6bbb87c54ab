import express, { Request, Response, NextFunction } from "express";
import { validateRequest } from "@/middleware/validateRequest";
import { rawRowsSchema } from "@acr/common/schemas/classify";
import { classifyRows } from "@/domain/classify";

const router = express.Router();

router.post(
  "/",
  validateRequest("POST", rawRowsSchema),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const suggestedTables = classifyRows(req.validatedAttributes as unknown as unknown[]);
      res.status(200).json({ suggestedTables, rows: req.validatedAttributes });
    } catch (error) {
      next(error);
    }
  },
);

export const classifierRoutes = router;
