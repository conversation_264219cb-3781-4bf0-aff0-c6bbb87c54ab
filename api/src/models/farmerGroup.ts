import { farmerGroupSchema, newFarmerGroupSchema } from "@acr/common/schemas/farmerGroup";
import { FarmerGroup, NewFarmerGroup } from "@acr/common/types/farmerGroup.types";
import { QueryResult } from "pg";

export const serialize = (farmerGroup: FarmerGroup | NewFarmerGroup): any[] => {
  if ("id" in farmerGroup) {
    const parsed = farmerGroupSchema.parse(farmerGroup);

    return [
      parsed.id,
      parsed.name,
    ]
  }

  const parsed = newFarmerGroupSchema.parse(farmerGroup);

  return [
    null,
    parsed.name,
  ]
};

export const deserialize = (result: QueryResult): FarmerGroup[] => {
  return result.rows.map((row) => {
    const farmerGroup = {
      ...row,
      createdAt: row.created_at
    };

    return farmerGroupSchema.parse(farmerGroup);
  });
};
