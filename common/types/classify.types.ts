import { z } from "zod";
import { rawRowsSchema, classifyFarmerSchema, classifyActivitySchema, classifyContractSchema } from "../schemas/classify";

export type RawRows = z.infer<typeof rawRowsSchema>;
export type ClassifyFarmer = z.infer<typeof classifyFarmerSchema>;
export type ClassifyActivity = z.infer<typeof classifyActivitySchema>;
export type ClassifyContract = z.infer<typeof classifyContractSchema>;
export type AnyClassified = (ClassifyFarmer[] | ClassifyActivity[] | ClassifyContract[])[]
